#!/usr/bin/env python3
"""
Test script for the expand_range function
"""

def expand_range(range_str):
    """
    Convert range strings into a list of strings. Handles multiple formats:
    - Discrete: 'N6B-027.001' -> ['N6B-027.001']
    - Range: 'N6B-027.003-005' -> ['N6B-027.003', 'N6B-027.004', 'N6B-027.005']
    - Discrete list: 'N6B-027.001,003,004' -> ['N6B-027.001', 'N6B-027.003', 'N6B-027.004']
    - Mixed: 'N6B-027.003-005,007' -> ['N6B-027.003', 'N6B-027.004', 'N6B-027.005', 'N6B-027.007']
    """
    # If no period in string, return as is
    if '.' not in range_str:
        return [range_str]
    
    # Split into base part and number part
    base_part = range_str.rsplit('.', 1)[0]  # Gets 'N6B-027'
    number_part = range_str.rsplit('.', 1)[1]  # Gets '001' or '003-005' or '001,003,004'
    
    result = []
    
    # Handle comma-separated values (can include ranges)
    if ',' in number_part:
        parts = number_part.split(',')
        for part in parts:
            if '-' in part:
                # This part is a range
                start, end = part.split('-')
                padding = len(start)
                start_num = int(start)
                end_num = int(end)
                result.extend([f"{base_part}.{str(i).zfill(padding)}" for i in range(start_num, end_num + 1)])
            else:
                # This part is a discrete value
                result.append(f"{base_part}.{part}")
    else:
        # Single value or single range
        if '-' in number_part:
            # Handle single range
            start, end = number_part.split('-')
            padding = len(start)
            start_num = int(start)
            end_num = int(end)
            result = [f"{base_part}.{str(i).zfill(padding)}" for i in range(start_num, end_num + 1)]
        else:
            # Single discrete value
            result = [range_str]
    
    return result

def test_expand_range():
    """Test the expand_range function with various inputs"""
    
    test_cases = [
        # Test case: (input, expected_output, description)
        ("N6B-027.001", ["N6B-027.001"], "Single discrete value"),
        ("N6B-027.003-005", ["N6B-027.003", "N6B-027.004", "N6B-027.005"], "Simple range"),
        ("N6B-027.001,003,004", ["N6B-027.001", "N6B-027.003", "N6B-027.004"], "Discrete list"),
        ("N6B-027.003-005,007", ["N6B-027.003", "N6B-027.004", "N6B-027.005", "N6B-027.007"], "Mixed range and discrete"),
        ("A-011.001,003", ["A-011.001", "A-011.003"], "Two discrete values"),
        ("A-012.001-003,006,009-010", ["A-012.001", "A-012.002", "A-012.003", "A-012.006", "A-012.009", "A-012.010"], "Complex mixed"),
        ("NoPoint", ["NoPoint"], "No period in string"),
    ]
    
    print("Testing expand_range function:")
    print("=" * 60)
    
    all_passed = True
    
    for i, (input_str, expected, description) in enumerate(test_cases, 1):
        result = expand_range(input_str)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"Test {i}: {status}")
        print(f"  Description: {description}")
        print(f"  Input: '{input_str}'")
        print(f"  Expected: {expected}")
        print(f"  Got:      {result}")
        if not passed:
            print(f"  ❌ Mismatch!")
        print()
    
    print("=" * 60)
    if all_passed:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return all_passed

if __name__ == "__main__":
    test_expand_range()
