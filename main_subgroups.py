import pandas as pd
import sys
import os
import numpy as np
from tabulate import tabulate
import textwrap
from collections import Counter
import json

########################################################################################

# Functions

def table_processor(df): # Esta função estrutura as tabelas

    cols_to_drop = ["Unnamed: 0", "Unnamed: 19"]
    cols = ["ID", "Node", "Element", "End", "CS", "MAT", "NMAX", "NMIN", "VYMAX", "VYMIN", 
            "VZMAX", "VZMIN", "MXMAX", "MXMIN", "MYMAX", "MYMIN", "MZMAX", "MZMIN"]
    
    force_cols = ["NMAX", "NMIN", "VYMAX", "VY<PERSON><PERSON>", "VZMAX", "VZMI<PERSON>", 
                 "<PERSON>XMA<PERSON>", "MXMI<PERSON>", "MYMAX", "MYMI<PERSON>", "MZMAX", "MZMIN"]
    
    df = df.drop(columns=cols_to_drop, errors="ignore")
    df.columns = cols
    
    # Convert ID column to string, replacing NaN with empty string
    df['ID'] = df['ID'].fillna('').astype(str)
    
    # Find rows with any force value but no ID
    has_forces = df[force_cols].notna().any(axis=1)
    no_id = df['ID'].str.strip() == ''
    problematic_rows = df[has_forces & no_id]
    
    if not problematic_rows.empty:
        print("\n⚠️   Error: Found rows with force values but no ID assigned:")
        for idx, row in problematic_rows.iterrows():
            forces_present = [col for col in force_cols if pd.notna(row[col])]
            print(f"    → Excel Row {idx + 16}: Has values in columns {', '.join(forces_present)}")
        sys.exit("\nExiting the script due to invalid data structure.")
    
    nan_indices = df[df.isna().all(axis=1)].index
    rows_to_drop = nan_indices.union(nan_indices + 1)
    df = df.drop(rows_to_drop, errors="ignore").reset_index(drop=True)
    df = df.round(2)
    return df

def get_extremes_dataframes(final_df, max_cols, min_cols):
    def safe_convert_id(id_val):
        if pd.isna(id_val):
            return ''
        try:
            # Convert float to int to remove decimal places, then to string
            if isinstance(id_val, float):
                return str(int(id_val))
            return str(id_val)
        except (ValueError, TypeError):
            return str(id_val)

    grouped = final_df.groupby('CS')
    values_list = []
    ids_list = []
    
    for cs, group in grouped:
        values_row = {'CS': cs}
        ids_row = {'CS': cs}
        
        # Handle maximum values
        for col in max_cols:
            max_value = group[col].max()
            # Convert IDs to string before joining
            max_ids = [safe_convert_id(x) for x in group.loc[group[col] == max_value, 'ID'].tolist()]
            max_ids = [id for id in max_ids if id and id.strip()]  # Remove empty strings
            values_row[col] = max_value
            ids_row[f"{col}_ID"] = ", ".join(max_ids) if max_ids else ""
        
        # Handle minimum values
        for col in min_cols:
            min_value = group[col].min()
            # Convert IDs to string before joining
            min_ids = [safe_convert_id(x) for x in group.loc[group[col] == min_value, 'ID'].tolist()]
            min_ids = [id for id in min_ids if id and id.strip()]  # Remove empty strings
            values_row[col] = min_value
            ids_row[f"{col}_ID"] = ", ".join(min_ids) if min_ids else ""
        
        values_list.append(values_row)
        ids_list.append(ids_row)

    values_df = pd.DataFrame(values_list)
    ids_df = pd.DataFrame(ids_list)
    
    return values_df, ids_df

def wrap_text(df, width=20): # Colocas os textos longos de cada célula em linhas diferentes
     
    return df.apply(lambda col: col.apply(lambda x: "\n".join(textwrap.wrap(str(x), width)) if isinstance(x, str) else x))

def check_files_exist(connections):
    # Get unique base names (before the last point) for file checking
    base_variants = {variant.rsplit('.', 1)[0] for variant in connections}
    for base_variant in base_variants:
        file_path = os.path.join(os.getcwd(), '..',f'{base_variant}.xlsm')
        #file_path = os.path.join(data_folder, f'{base_variant}.xlsm')
        if not os.path.exists(file_path):
            print(f"⚠️   Error: File for connection {base_variant} not found at {file_path}.")
            sys.exit("Exiting the script due to missing files.")
    print("\n")

#########################################################################################


def expand_range(range_str):
    """
    Convert range strings into a list of strings. Handles multiple formats:
    - Discrete: 'N6B-027.001' -> ['N6B-027.001']
    - Range: 'N6B-027.003-005' -> ['N6B-027.003', 'N6B-027.004', 'N6B-027.005']
    - Discrete list: 'N6B-027.001,003,004' -> ['N6B-027.001', 'N6B-027.003', 'N6B-027.004']
    - Mixed: 'N6B-027.003-005,007' -> ['N6B-027.003', 'N6B-027.004', 'N6B-027.005', 'N6B-027.007']
    """
    # If no period in string, return as is
    if '.' not in range_str:
        return [range_str]

    # Split into base part and number part
    base_part = range_str.rsplit('.', 1)[0]  # Gets 'N6B-027'
    number_part = range_str.rsplit('.', 1)[1]  # Gets '001' or '003-005' or '001,003,004'

    result = []

    # Handle comma-separated values (can include ranges)
    if ',' in number_part:
        parts = number_part.split(',')
        for part in parts:
            if '-' in part:
                # This part is a range
                start, end = part.split('-')
                padding = len(start)
                start_num = int(start)
                end_num = int(end)
                result.extend([f"{base_part}.{str(i).zfill(padding)}" for i in range(start_num, end_num + 1)])
            else:
                # This part is a discrete value
                result.append(f"{base_part}.{part}")
    else:
        # Single value or single range
        if '-' in number_part:
            # Handle single range
            start, end = number_part.split('-')
            padding = len(start)
            start_num = int(start)
            end_num = int(end)
            result = [f"{base_part}.{str(i).zfill(padding)}" for i in range(start_num, end_num + 1)]
        else:
            # Single discrete value
            result = [range_str]

    return result

def Envelopes(sheet_name, sheet_name_members, members_info, connection_group,envelope_factor,operation_choice):
    print(f"\n🔹 Start of Data Reading") 

    # Define max and min columns at the start
    max_cols = ['NMAX', 'VYMAX', 'VZMAX', 'MXMAX', 'MYMAX', 'MZMAX']
    min_cols = ['NMIN', 'VYMIN', 'VZMIN', 'MXMIN', 'MYMIN', 'MZMIN']

    # Extract IDs to exclude and base groups
    variants_to_include = []
    base_connections = set()
    
    # Process the single connection group
    for variant_base, variant_ranges in connection_group.items():
        # Add the variant base to base_connections
        base_connections.add(variant_base)  # e.g., "N6C-002"
        
        # Process the ranges to exclude
        for variant_range in variant_ranges:
            expanded_variants = expand_range(variant_range)
            variants_to_include.extend(expanded_variants)

    check_files_exist( base_connections)

    # Read Connections
    GlobalConnections = []

    # Process each base connection
    for base_variant in base_connections:
        file_path = os.path.join(os.getcwd(), '..',f'{base_variant}.xlsm')
        file_path = os.path.abspath(file_path)

        #file_path = os.path.join(data_folder, f'{base_variant}.xlsm')
        df = pd.read_excel(file_path, sheet_name, skiprows=14)
        print(f"🔹 Connection {base_variant} has been Read ✔️ ")
        df = df.round(2)
        df = table_processor(df)
        
        # Filter DataFrame to EXCLUDE specified IDs
        if variants_to_include:
            print(f"🔹 Before filtering: {len(df)} rows")
            print(f"🔹 Operation choice: {operation_choice}")
            print(f"🔹 Variants to include: {variants_to_include[:5]}...")  # Show first 5
            print(f"🔹 Sample IDs in DataFrame: {df['ID'].head().tolist()}")

            if operation_choice == "e":  # Changed from "Exclude" to "e"
                df = df[~df['ID'].isin(variants_to_include)]
                print(f"🔹 After EXCLUDING: {len(df)} rows")
            else:  # operation_choice == "j" for join/include
                df = df[df['ID'].isin(variants_to_include)]
                print(f"🔹 After INCLUDING: {len(df)} rows")

        if not df.empty:
            GlobalConnections.append(df)
            print(f"🔹 Added DataFrame with {len(df)} rows to GlobalConnections")
        else:
            print(f"🔹 DataFrame is empty after filtering - not added to GlobalConnections")

    # Check if GlobalConnections is empty before concatenating
    if not GlobalConnections:
        print("\n❌ ERROR: No data found after filtering!")
        print("🔹 This could happen if:")
        print("   - The filter criteria don't match any IDs in the Excel files")
        print("   - The operation choice (join/exclude) is removing all data")
        print("   - The Excel files don't contain the expected data structure")
        return None

    final_df = pd.concat(GlobalConnections, ignore_index=True)
    print(f"\n✅ Successfully Created the Global DataFrame with {len(final_df)} rows ✔️")

    # Remove rows where all force columns are NaN
    force_cols = max_cols + min_cols
    final_df = final_df.dropna(subset=force_cols, how='all')

    # Read Members file
    file_memb_path = os.path.join(os.getcwd(), '..',f'{members_info}.xlsm')

    if os.path.exists(file_memb_path):
        
        memb = pd.read_excel(file_memb_path, sheet_name_members, skiprows=2)

        memb = memb.round(2)

        # Create mapping dictionary from Element to info
        element_info_dict = dict(zip(memb.iloc[:, 0], memb.iloc[:, 15]))
        
        # Update CS column by mapping Elements to their info
        final_df['Member_Info'] = final_df['Element'].map(element_info_dict).fillna('')
        final_df['CS'] = final_df['CS'].astype(str) + ' ' + final_df['Member_Info']
        final_df = final_df.drop('Member_Info', axis=1)  # Remove temporary column

        print("\n Successfully Updated CS Column ✔️")
    else:
        print(f"\n⚠️ Warning: Members file '{members_info}.xlsm' not found. Continuing without member information.")

    # Ensure force columns are numeric and remove any remaining NaN rows
    for col in max_cols + min_cols:
        final_df[col] = pd.to_numeric(final_df[col], errors='coerce')
    
    final_df = final_df.dropna(subset=max_cols + min_cols, how='all')

    # Get the values and IDs dataframes
    values_df, ids_df = get_extremes_dataframes(final_df, max_cols, min_cols)

    ordered_columns = ["CS", "NMAX", "NMIN", "VYMAX", "VYMIN", "VZMAX", "VZMIN", "MXMAX", "MXMIN", "MYMAX", "MYMIN", "MZMAX", "MZMIN"]

    values_df = values_df[ordered_columns]

    ordered_columns_with_id = []
    for col in ordered_columns:
        if col.endswith("MAX") or col.endswith("MIN"):
            ordered_columns_with_id.append(col + "_ID")  # Adiciona _ID às colunas MAX e MIN
        else:
            ordered_columns_with_id.append(col)

    ids_df = ids_df[ordered_columns_with_id]

    ids_df[ordered_columns_with_id] = ids_df[ordered_columns_with_id].apply(lambda col: col.apply(lambda x: ', '.join(dict.fromkeys(map(str.strip, str(x).split(','))))))  # Remove duplicados

    envonventemaximi_df = values_df.copy()
    force_cols = max_cols + min_cols
    
    # Fill NaN values with 0 before multiplication and rounding
    envonventemaximi_df[force_cols] = (
        envonventemaximi_df[force_cols]
        .fillna(0)  # Replace NaN with 0
        .multiply(float(envelope_factor[0]))  # Multiply by 1.2
        .round()  # Round to nearest whole number
        .astype(int)  # Convert to integer
    )

    wrapped_ids_df = ids_df.copy()
    wrapped_ids_df.iloc[:, 1:] = wrap_text(wrapped_ids_df.iloc[:, 1:], width=15) #Ajusta o texto em cada célula

    # Elimina od ID's cujo valor é próximo de zero da tabela de ID's Envolvente 
    values_df_aux = values_df.apply(pd.to_numeric, errors='coerce')

    #mask = values_df_aux.apply(lambda x: x.between(-0.5, 0.5)) # Criar máscara para valores entre -0.5 e 0.5
    mask = values_df_aux.apply(lambda x: x.between(-10, 10))

    indices = mask.stack()[mask.stack()].index
    row_numbers = indices.get_level_values(0).tolist()  # Números das linhas a alterar
    col_numbers = [values_df.columns.get_loc(col) for col in indices.get_level_values(1).tolist()] # Número das colunas a alterar 

    for row, col in zip(row_numbers, col_numbers):  # Zeroa a célula da Matrix de ID's cujo valor é zero na Envolvente
        wrapped_ids_df.iloc[row, col] = ''

    #########################################################################################
    #Sugerir a ligação para Análise  
    cols_to_check = wrapped_ids_df.columns[1:13]
    all_ids = wrapped_ids_df[cols_to_check].values.flatten()


    # Exibir os IDs e suas contagens
    all_ids = [str(id).replace('\n', '') for id in all_ids if str(id).strip() != '']
    all_ids = ', '.join(all_ids)
    segments = all_ids.split(',')
    segments = [segment.strip() for segment in segments]
    segment_counts = Counter(segments)
    most_common_segment = segment_counts.most_common(2)

    #########################################################################################
    
    print("\n📌 \033[1;32mEnvelope Corresponding IDs:\033[0m ")
    print(tabulate(wrapped_ids_df, headers='keys', tablefmt='fancy_grid', showindex=False))

    if len(most_common_segment) > 0:
        first_most_common_segment, first_count = most_common_segment[0]
        print(f"\n  → The connection with higher forces is: {first_most_common_segment}.")

    if len(most_common_segment) > 1:
        second_most_common_segment, second_count = most_common_segment[1]
        print(f"\n  → The second connection with higher forces is: {second_most_common_segment}.")

    print("\n📌 \033[1;32mEnvelope Values:\033[0m ")
    print(tabulate(values_df, headers='keys', tablefmt='fancy_grid', showindex=False))

    print(f"\n📌 \033[1;32mMaximized Envelope Values ({envelope_factor[0]} x Envelope):\033[0m ")
    print(tabulate(envonventemaximi_df, headers='keys', tablefmt='fancy_grid', showindex=False))


def main():
    try:
        with open('config_subgroups.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("⚠️   Error: config file not found")
        sys.exit(1)
    except json.JSONDecodeError:
        print("⚠️   Error: Invalid JSON in config")
        sys.exit(1)

    # Get configuration values
    sheet_name = config['sheet_name']
    sheet_name_members = config['sheet_name_members']
    members_info = config['members_info']
    envelope_factor = config['envelope_factor']
    
    # Mostrar grupos disponíveis
    available_groups = list(config['connection_groups'].keys())
    print("\nAvailable connection groups:")
    for i, group in enumerate(available_groups, 1):
        print(f"{i}. {group}")
    
    # Seleção do grupo
    if len(sys.argv) > 1:
        group_name = sys.argv[1]
        if group_name not in config['connection_groups']:
            print(f"⚠️   Error: Connection group '{group_name}' not found in config")
            print("Available groups:", ", ".join(config['connection_groups'].keys()))
            sys.exit(1)
    else:
        while True:
            try:
                choice = input("\nSelect a connection group (enter number): ")
                index = int(choice) - 1
                if 0 <= index < len(available_groups):
                    group_name = available_groups[index]
                    break
                else:
                    print(f"Please enter a number between 1 and {len(available_groups)}")
            except ValueError:
                print("Please enter a valid number")

        while True:
            try:
                operation_choice = input("\nSelect Join(j) or Exclude(e) connection groups (enter number): ")
                if operation_choice =='j' or operation_choice =='e':
                    break                    
                else:    
                    print("Please enter the operation join(j) or exclude(e)")
                    
            except ValueError:
                print("Please enter a valid option")    

    selected_group = config['connection_groups'][group_name]
    #print("\nSelected group structure:", json.dumps(selected_group, indent=2))  # Debug print
    Envelopes(sheet_name, sheet_name_members, members_info, selected_group, envelope_factor, operation_choice)

if __name__ == "__main__":
    main()
